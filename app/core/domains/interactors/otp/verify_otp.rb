# frozen_string_literal: true

class Interactors::Otp::VerifyOtp < Interactors::AbstractIteractor
  contract do
    params do
      required(:organization_id).filled(:string)
      required(:user_id).filled(:string)
      required(:scope).filled(:string)
      required(:otp).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    return Success(Hashie::Mash.new(success: true)) if Rails.env.test?

    user = Models::User.find_by(id: params[:user_id])
    return Failure 'User not found' if user.nil?

    response = MekariSso::Services::VerifyOtp.new(sso_id: user.sso_id, email: user.email, otp: params[:otp]).call

    if response.success?
      Success(Hashie::Mash.new(success: true))
    else
      args = {}
      args[:params] = params
      args[:response] = response.failure
      Rails.logger.error(
        message: 'Failed to verify OTP',
        class:   self.class.name,
        method:  __method__,
        args:    args,
      )
      Rollbar.error('Could not verify OTP', class: self.class, method: __method__, args: args)
      Failure 'Invalid code. Please try again.'
    end
  end
end
