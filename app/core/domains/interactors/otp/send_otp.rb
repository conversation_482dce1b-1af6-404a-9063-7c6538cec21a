# frozen_string_literal: true

class Interactors::Otp::SendOtp < Interactors::AbstractIteractor
  contract do
    params do
      required(:organization_id).filled(:string)
      required(:user_id).filled(:string)
      required(:scope).filled(:string)
      required(:channel).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    session = Services::Redis::Otp::GetSession.new(params[:scope], params[:user_id]).call
    return Success(Hashie::Mash.new(success: true)) if Rails.env.test?

    if session.nil?
      user = Models::User.find_by(id: params[:user_id])
      return Failure 'User not found' if user.nil?

      response = MekariSso::Services::SendOtp.new(sso_id: user.sso_id, email: user.email).call

      if response.success?
        counter = Services::Redis::Otp::GetCounter.new(params[:scope]).call
        Services::Redis::Otp::SetSession.new(params[:scope], params[:user_id]).call
        return Success(Hashie::Mash.new(success: true, counter: counter.to_i))
      else
        args = {}
        args[:params] = params
        args[:response] = response.failure
        Rails.logger.error(
          message: 'Failed to Request OTP',
          class:   self.class.name,
          method:  __method__,
          args:    args,
        )
        Rollbar.error('Could not send OTP', class: self.class, method: __method__, args: args)
        return Failure 'Could not send OTP'
      end
    end

    now = Time.now.to_i
    cnt = Services::Redis::Otp::GetCounter.new(params[:scope]).call
    counter = cnt.to_i
    counter -= (now - session.to_i)
    Success(Hashie::Mash.new(success: false, counter: counter.to_i))
  end
end
