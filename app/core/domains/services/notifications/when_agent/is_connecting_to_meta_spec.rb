# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Services::Notifications::WhenAgent::IsConnectingToMeta, type: :service do
  describe '#build_payload' do
    let(:notification) { { title: 'Test Title', body: 'Test Body' } }
    let(:data) { { key: 'value' } }
    let(:type) { :notif }
    let(:web_click_action) { nil }
    let(:service) { described_class.new(data: {}, registration_ids: [], organization_id: nil) }

    it 'returns a payload with apns headers' do
      payload = service.send(:build_payload, notification, data, type, web_click_action)
      expect(payload[:apns]).to include(headers: described_class::VOICE_APNS_HEADERS)
    end
  end
end
