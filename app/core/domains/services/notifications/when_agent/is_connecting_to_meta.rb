# frozen_string_literal: true

class Services::Notifications::WhenAgent::IsConnectingToMeta < Services::Notifications::AbstractNotification
  def initialize(args)
    connect_fcm
    @data             = Hashie::Mash.new(args[:data].to_h)
    @registration_ids = args[:registration_ids]
    @organization_id  = args[:organization_id]
    @apns_headers = VOICE_APNS_HEADERS
  end

  def call
    return unless @registration_ids.present?
    notification = {
      title: 'System - Calls webhook',
      body:  'agent is connecting to meta'
    }
    push_v1(registration_ids: @registration_ids,
            notification:     notification,
            data:             @data.except(:from),
            type:             :notif,
            event_type:       'when_system_send_incoming_calls_webhook',
            organization_id:  @organization_id,
            write_log:        true)
  end
end
