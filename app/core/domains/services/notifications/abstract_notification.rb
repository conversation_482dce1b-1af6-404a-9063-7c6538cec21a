# frozen_string_literal: true

require 'ably'
class Services::Notifications::AbstractNotification
  implements_interface Services::Elasticsearch::Queries::Find
  implements_interface Services::Elasticsearch::Queries::FindBy
  implements_interface Services::Elasticsearch::Queries::Where
  implements_interface Services::Elasticsearch::Queries::Exists

  include Dry::Monads[:result]
  attr_accessor :resource, :notification

  VOICE_APNS_HEADERS = {
    'apns-push-type': 'voip',
    'apns-priority':  '10',
    'apns-topic':     ENV['VOICE_APNS_TOPIC']
  }.freeze
  public_constant :VOICE_APNS_HEADERS

  def ids_group
    if ENV['REGISTRATION_IDS_GROUP'].to_i > 0
      ENV['REGISTRATION_IDS_GROUP'].to_i
    else
      1_000
    end
  end

  def push(registration_ids:, notification: {}, data: {}, type: :data, device_type: nil, triggered_at: nil, metric_event_type: nil)
    data = data.with_indifferent_access
    return nil unless device_type.present?
    payload = {
      priority:          'high',
      content_available: true
    }
    if type.to_sym.eql?(:notif)
      notification[:body] = helper.strip_tags(notification[:body])
      notification[:sound] = 'default' unless device_type.eql?(:web)
      payload = payload.merge(notification: notification)
    end
    if data.present?
      if exceed_limits?(data, payload)
        data = reduce_data_payload(data)
        payload[:notification][:body] = payload.dig(:notification, :body).truncate_bytes(105) if data&.dig(:notification, :body)&.bytesize.to_i > 100
      end
      payload = payload.merge(data: data)
    end

    case device_type
    when :android
      send_android(registration_ids, payload, triggered_at: triggered_at, metric_event_type: metric_event_type)
    when :web
      send_web(registration_ids, payload, triggered_at: triggered_at, metric_event_type: metric_event_type)
    when :ios
      send_ios(registration_ids, payload, triggered_at: triggered_at, metric_event_type: metric_event_type)
    end
  end

  def prepare_registration_ids(auth_ids)
    auth_ids = [auth_ids].flatten
    if auth_ids.present?
      tokens = Models::NotificationCredential.where(auth_id: auth_ids).group_by_resource_type
    else
      tokens = {}
    end
    tokens
  end

  def prepare_app_chat_registration_ids(contact_id, channel_id)
    Models::AppChatNotificationCredential.where(contact_id: contact_id, channel_integration_id: channel_id).select(:token).pluck(:token)
  end

  def prepare_registration_ids_v1(auth_ids, resource_type: [])
    auth_ids = [auth_ids].flatten
    if auth_ids.present?
      params = { auth_id: auth_ids }
      params[:resource_type] = resource_type if resource_type.present?

      tokens = Models::NotificationCredential.where(params).select(:token).pluck(:token)
    else
      tokens = []
    end
    tokens
  end

  def push_ably(channel_name, user_token, payload, _ = nil)
    channel = @ably_service.channel(channel_name)
    begin
      unless Rails.env.test?
        send = channel.publish(user_token, payload)
        tags = send ? 'ably:Success' : 'ably:Fail'
        Services::Datadog::CaptureCustomMetric.new(name: :push_notification, tags: [tags]).capture
      end
    rescue => e
      Rollbar.error(e, class: 'Services::Notifications::AbstractNotification', method: 'push_ably', args: { channel_name: channel_name, user_token: user_token, payload: payload })
      Services::Datadog::CaptureCustomMetric.new(name: :push_notification, tags: ['ably:Fail']).capture
    end
  end

  def push_v1(registration_ids:, notification:, data:, type:, web_click_action: nil, triggered_at: nil, event_type: nil, organization_id: nil, write_log: false)
    return unless registration_ids.present?

    fcm_client = Services::Firebase::FcmV1.new @json_key, write_log

    payload = build_payload notification, data, type, web_click_action
    if data.present? && exceed_limits?(payload, {})
      payload = build_payload_smaller notification, data, type, web_click_action
    end

    messages = registration_ids.map { |id| { message: payload.merge(token: id) } }
    messages.in_groups_of(ids_group, false).map do |message_jsons|
      # ids = message_jsons.map { |message| message[:message][:token] }
      results = fcm_client.batch_push(message_jsons, triggered_at: triggered_at, event_type: event_type, organization_id: organization_id)
      invalid_tokens = []
      retry_messages = []
      results.each_with_index { |result, index|
        invalid_tokens << message_jsons[index].dig(:message, :token) if result&.code.to_i == 404
        retry_messages << message_jsons[index] if [*500..599, 429, 0].include?(result&.code.to_i)
      }

      handle_invalid_tokens invalid_tokens if invalid_tokens.present?
      handle_retry retry_messages if retry_messages.present?
    end
  end

  def define_resource_type(room_type)
    return ['web'] if !Services::Preference.new.enabled?(:enable_wa_group_mobile_notification) && room_type == Models::GroupServiceRoom.to_s

    []
  end

  # Publishes log notification to sidekiq for stored the sent log in database
  #
  # @return [void]
  def publish_mqtt_log_notification(organization_id, recipient_ids, data, triggered_at, log_flag_name)
    return if log_flag_name.nil?
    return unless REDIS_R.get(log_flag_name).to_s.eql?('true')

    log_params = {
      event_id:        data[:event_id],
      event_type:      data[:event_type],
      organization_id: organization_id,
      recipient_ids:   recipient_ids,
      data:            data,
      triggered_at:    triggered_at,
      provider:        'mqtt'
    }

    Log::TriggeredLogNotificationWorker.perform_async(log_params)
  end

  # Publishes the notification to the MQTT queue if enabled for the organization.
  def mqtt_notification_enabled?(flag_name, organization_id)
    REDIS_R.get("mqtt::#{flag_name}::#{organization_id}").to_s.eql?('true')
  end

  private

  def fetch_receiver_id_to_sso_map data
    room = Hashie::Mash.new(data[:extra][:room].to_h)
    notifiable_agent_ids = [data[:notifiable][:id]]

    # Currently only handle agent and member roles
    agent_receiver_ids = Services::Room::GetParticipantId.get_agent_ids_only(room, notifiable_agent_ids)

    id_to_sso_map = {}
    id_pairs = Models::User.where(id: agent_receiver_ids).pluck(:id, :sso_id, :role)
    id_pairs.each do |id_pair|
      # User Map is in the form of {id => [sso_id, role]}
      sso_id_and_role = id_pair[1].present? ? [id_pair[1], id_pair[2]] : nil
      id_to_sso_map[id_pair[0]] = sso_id_and_role
    end
    id_to_sso_map
  end

  def strip_tags(html)
    ActionView::Base.full_sanitizer.sanitize(html)
  end

  def notif_for(old_notif, device_type)
    new_notif        = old_notif.clone
    new_notif[:body] = helper.strip_tags(new_notif[:body])
    new_notif
  end

  def connect_fcm
    if Services::Preference.new.enabled?(:use_andpush)
      @notif_service = Services::Andpush::Fcm.new(ENV['FCM_SERVER_KEY'])
    else
      @notif_service = Services::Firebase::Fcm.new(ENV['FCM_SERVER_KEY'])
    end
  end

  def connect_fcm_webchat
    @notif_service = Services::Firebase::Fcm.new(ENV['WEBCHAT_FCM_SERVER_KEY'])
  end

  def connect_fcm_app_chat(server_key)
    if Services::Preference.new.enabled?(:use_andpush)
      @notif_service = Services::Andpush::Fcm.new(server_key)
    else
      @notif_service = Services::Firebase::Fcm.new(server_key)
    end
  end

  def send_android registration_ids, payload, triggered_at: nil, metric_event_type: nil
    reg_ids = registration_ids['android'] rescue []
    if reg_ids.blank?
      reg_ids = registration_ids['android'] rescue []
    end
    if reg_ids.blank? && registration_ids.is_a?(Array)
      reg_ids = [registration_ids].flatten
    end
    if reg_ids.present?
      response = []
      organization_id = reg_ids.first&.organization_id
      reg_ids.in_groups_of(ids_group, false).each do |regs|
        res = @notif_service.send(regs.pluck(:token), payload, triggered_at: triggered_at, metric_event_type: metric_event_type, log_attr: regs.pluck(:auth_id, :token), organization_id: organization_id, device_type: 'android')
        response << { data: res, payload: payload }
      end
      Services::Datadog::CaptureCustomMetric.new(name: :service_notification_process, tags: ['registration_ids_size']).capture(action: :histogram, count: reg_ids.length)
      response
    end
  rescue => e
    Rollbar.error(e, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
  end

  def send_web registration_ids, payload, triggered_at: nil, metric_event_type: nil
    reg_ids = registration_ids['web'] rescue []
    if reg_ids.blank?
      reg_ids = registration_ids['web'] rescue []
    end
    if reg_ids.blank? && registration_ids.is_a?(Array)
      reg_ids = [registration_ids].flatten
    end
    if reg_ids.present?
      response = []
      organization_id = reg_ids.first&.organization_id
      reg_ids.in_groups_of(ids_group, false).each do |regs|
        res = @notif_service.send(regs.pluck(:token), payload, triggered_at: triggered_at, metric_event_type: metric_event_type, log_attr: regs.pluck(:auth_id, :token), organization_id: organization_id, device_type: 'web')
        response << { data: res, payload: payload }
      end
      Services::Datadog::CaptureCustomMetric.new(name: :service_notification_process, tags: ['registration_ids_size']).capture(action: :histogram, count: reg_ids.length)
      response
    end
  rescue => e
    Rollbar.error(e, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
  end

  def send_ios registration_ids, payload, triggered_at: nil, metric_event_type: nil
    reg_ids = registration_ids['ios'] rescue []
    if reg_ids.blank?
      reg_ids = registration_ids['ios'] rescue []
    end
    if reg_ids.blank? && registration_ids.is_a?(Array)
      reg_ids = [registration_ids].flatten
    end
    if reg_ids.present?
      response = []
      organization_id = reg_ids.first&.organization_id
      reg_ids.in_groups_of(ids_group, false).each do |regs|
        res = @notif_service.send(regs.pluck(:token), payload, triggered_at: triggered_at, metric_event_type: metric_event_type, log_attr: regs.pluck(:auth_id, :token), organization_id: organization_id, device_type: 'ios')
        response << { data: res, payload: payload }
      end
      Services::Datadog::CaptureCustomMetric.new(name: :service_notification_process, tags: ['registration_ids_size']).capture(action: :histogram, count: reg_ids.length)
      response
    end
  rescue => e
    Rollbar.error(e, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
  end

  def set_owner_history notification_log_id:, notif_able_id:, notif_able_type:
    history                     = Models::NotificationHistory.new
    history.notif_able_type     = notif_able_type
    history.notif_able_id       = notif_able_id
    history.is_readed           = false
    history.notification_log_id = notification_log_id
    history.save
  end

  # With history in DB
  def write(body, notifiable, notifier, receiver)
    data   = realtime_data(body, notifiable, notifier, receiver).except(:type)
    result = Repositories::Notifications::Create.new(data).call
    result.success? ? result.value! : data.merge!(type: 'Models::Notification', is_read: false)
  end

  # Only PN
  def realtime_data(body, notifiable, notifier, receiver)
    {
      title:           body[:title],
      body:            body[:body],
      notif_type:      @notif_type,
      event_type:      @event_type,
      notifiable_id:   notifiable[:id],
      notifiable_type: notifiable[:type],
      data:            body,
      notifier_id:     notifier[:id],
      notifier_type:   notifier[:type],
      receiver_id:     receiver[:id],
      receiver_type:   receiver[:type],
      click_action:    body[:click_action],
      type:            'PushNotification'
    }
  end

  def realtime_data_without_receiver(body, notifiable, notifier)
    {
      title:           body[:title],
      body:            body[:body],
      notif_type:      @notif_type,
      event_type:      @event_type,
      notifiable_id:   notifiable[:id],
      notifiable_type: notifiable[:type],
      data:            body,
      notifier_id:     notifier[:id],
      notifier_type:   notifier[:type],
      click_action:    body[:click_action],
      type:            'PushNotification'
    }
  end

  def result_with_timestamp(result)
    result.to_h.merge(timestamp: Time.now.to_i)
  end

  # helper methods represent of class helper sent message
  def helper
    # call class helper sent message
    Services::Notifications::HelperSentMessage.new
  end

  def initiate_ably
    @ably_service = Ably::Rest.new(key: ENV['ABLY_SERVER_KEY'])
  end

  def build_click_action(notification, device_type, click_action)
    device_type.to_s.eql?('web') ? notification.merge(click_action: click_action) : notification
  end

  def build_payload notification, data, type, web_click_action
    payload = {}
    if type.to_sym.eql?(:notif)
      notification[:body] = helper.strip_tags(notification[:body])
      payload[:notification] = notification
    end

    if data.present?
      payload_data = {}
      data.each { |a, b| payload_data[a] = !b.is_a?(String) && !b.nil? ? JSON.dump(b) : b }
      payload[:data] = payload_data
    end

    if payload[:data].present? && payload[:data]['session']&.include?('sdp')
      # Decode the escaped sdp content
      payload[:data]['session'] = payload[:data]['session']
        .gsub(/\\r/, "\r")  # Replace \r with actual carriage return
        .gsub(/\\n/, "\n")  # Replace \n with actual newline
    end

    payload.merge!(
      apns:    { payload: { aps: { sound: 'default', "content-available": 1 } } },
      android: { priority: 'high' },
      webpush: { headers: { Urgency: 'high' } }
    )

    if web_click_action.present?
      payload.deep_merge!(webpush: {
        notification: {
          click_action: web_click_action
        }
      })
    end
    payload
  end

  def build_smaller_notification(notification)
    if notification[:body].present?
      notification_body = helper.strip_tags(notification[:body])
      notification[:body] = notification_body&.bytesize.to_i > 100 ? notification_body.truncate_bytes(105) : notification_body
    end

    notification[:title] = notification[:title].truncate_bytes(105) if notification[:title]&.bytesize.to_i > 100
    notification
  end

  def build_smaller_room(room)
    if room&.dig('last_message').present?
      last_message = room['last_message']
      last_message['raw_message'] = nil
      last_message['text'] = last_message['text'].truncate_bytes(105) if last_message&.dig('text')&.bytesize.to_i > 100
      room['last_message'] = last_message
    end

    if room&.dig('name').present?
      room['name'] = room['name'].truncate_bytes(105)
    end

    if room&.dig('description').present?
      room['description'] = room['description'].truncate_bytes(105)
    end
    room
  end

  def build_payload_smaller notification, data, type, web_click_action
    payload = {}
    if type.to_sym.eql?(:notif)
      payload[:notification] = build_smaller_notification(notification)
    end

    if data.present?
      data = data.as_json
      payload_data = {}
      payload_data['is_partial'] = 'true'

      data['text'] = data['text'].truncate_bytes(105) if data&.dig('text')&.bytesize.to_i > 100
      data['extra'] = nil if data&.dig('extra').present?
      data['file'] = data&.dig('file')&.slice('high') if data&.dig('file').present?
      if data&.dig('sender', 'name').present?
        data['sender']['name'] = data['sender']['name'].truncate_bytes(105)
      end

      if data&.dig('cc').present?
        data['cc'] = []
      end

      if data&.dig('bcc').present?
        data['bcc'] = []
      end

      if data&.dig('attachments').present?
        data['attachments'] = []
      end

      if data&.dig('reply_to').present?
        data['reply_to'] = []
      end

      if data&.dig('room')
        data['room'] = build_smaller_room(data['room'])
      end

      if data&.dig('reply', 'type') == 'call_permission_request'
        data.dig('reply', 'sender')&.[]=('avatar', nil)
      end

      data.each do |a, b|
        payload_data[a] = !b.is_a?(String) && !b.nil? ? JSON.dump(b) : b
      end
      payload[:data] = payload_data
    end

    payload.merge!(
      apns:    { payload: { aps: { sound: 'default', "content-available": 1 } } },
      android: { priority: 'high' },
      webpush: { headers: { Urgency: 'high' } }
    )

    if web_click_action.present?
      payload.deep_merge!(webpush: {
        notification: {
          click_action: web_click_action
        }
      })
    end
    payload
  end

  # Only for Models with replica_db in shards connection config
  def switch_replica_db
    Services::Preference.new.enabled?(:read_from_replica) ? read_from_replica_db { yield } : yield
  end

  def read_from_replica_db
    ActiveRecord::Base.connected_to(role: :reading, shard: :replica_db) { yield }
  end

  def exceed_limits? data, payload
    "#{data}#{payload}".bytesize > 3500.bytes
  end

  def reduce_data_payload data
    data['is_partial'] = 'true'
    data['text'] = data['text'].truncate_bytes(105) if data&.dig('text')&.bytesize.to_i > 100
    data['reply'] = nil
    data['file'] = data&.dig('file')&.slice('high') if data&.dig('file').present?
    data
  end

  def enable_fcm_v1
    @fcm_v1 ||= Services::Preference.new.enabled?(:use_fcm_v1)
  end
  alias enable_fcm_v1? enable_fcm_v1

  def enable_fcm_v1_specific
    @fcm_v1_specific ||= Services::Preference.new.enabled?(:use_fcm_v1_specific, organization_id: @organization_id)
  end
  alias enable_fcm_v1_specific? enable_fcm_v1_specific

  def enable_improve_notif
    @improve_notif ||= Services::Preference.new.enabled?(:enable_improve_notif)
  end
  alias enable_improve_notif? enable_improve_notif

  def get_auths auth_ids
    Models::NotificationCredential.where(auth_id: auth_ids).group_by(&:auth_id)
  end

  def get_organization organization_id
    Services::Redis::Organizations::Get.new(organization_id).call
  end

  def get_registration_ids auth_id
    return prepare_registration_ids(auth_id) if enable_improve_notif?
    return @tokens&.dig(auth_id)&.group_by(&:resource_type) if @tokens.present?
    {}
  end

  def bulk_insert_psql records
    Models::Notification.import!(records)
  rescue => e
    Rollbar.error(e, class: 'Services::Notifications::AbstractNotification', method: 'bulk_insert_psql', args: { payload: records })
  end

  def handle_retry(messages)
    Notifications::RetryWorkerV1.new.perform(messages, @json_key) if Rails.env.test?
    Notifications::RetryWorkerV1.perform_async(messages, @json_key) unless Rails.env.test?
  end

  def handle_invalid_tokens(tokens)
    Models::NotificationCredential.where(token: tokens).destroy_all
  end

  # Publishes the notification to the MQTT queue if enabled for the organization.
  #
  # @return [void]
  def publish_mqtt_notification recipient_ids: nil, organization_id: nil, flag_name: nil, data: nil, triggered_at: Time.zone.now, is_organization_level: false, log_flag_name: nil
    return unless data.present?
    return unless organization_id.present? && mqtt_notification_enabled?(flag_name, organization_id)

    recipient_ids = Array(recipient_ids).map(&:to_s)

    mqtt_params = {
      recipient_ids:         recipient_ids,
      data:                  data,
      triggered_at:          triggered_at.is_a?(Integer) ? Time.at(triggered_at) : triggered_at,
      organization_id:       organization_id,
      is_organization_level: is_organization_level
    }
    KafkaProducers::Notifications::MqttProducers.new(mqtt_params).publish

    publish_mqtt_log_notification(organization_id, recipient_ids, data, triggered_at, log_flag_name)
  end

  def extract_organization_id
    org_id = @data.dig(:extra, :room, :organization_id)
    return org_id if org_id.present? && org_id.is_a?(String)

    Rollbar.error("invalid organization_id for room #{@data[:extra][:room][:id]}", class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
    nil
  end
end
