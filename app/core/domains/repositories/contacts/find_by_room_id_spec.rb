# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::Contacts::FindByRoomId do
  describe '#call' do
    subject(:result) { described_class.new(room_id: room_id, organization_id: organization_id, actor_id: actor_id).call }

    let(:room_id) { fake_uuid }
    let(:value)   { result.value_or(&:itself) }
    let(:organization_id) { fake_uuid }
    let(:actor_id) { fake_uuid }

    it 'returns contact', :aggregate_failures do
      stub_any_elasticsearch_request
      room    = create(:room0, id: room_id)
      contact = create(:contact, channel_integration: room.channel_integration)
      create :customer_participant, room: room, contact_able: contact

      expect(result).to be_success
      expect(value).to be_a(Entities::Contact)
      expect(value.id).to eq(contact.id)
      expect(value.qontak_customer_id).to eq(contact.qontak_customer_id)
    end

    it 'returns contact not found failure' do
      expect(result).to eq(Failure(:contact_not_found))
    end
  end
end
