# frozen_string_literal: true

class Repositories::Messages::UpdateMessageCallPermission < Repositories::AbstractRepository
  include Dry::Monads::Do.for(:call)

  attr_reader :id
  attr_reader :status

  def initialize(id, status)
    @id = id
    @status = status
  end

  def call
    return Failure(:message_id_missing) if @id.nil?
    message = find_message(@id)
    return Failure(:message_not_found) unless message
    return Failure(:message_not_call_permission_request) unless message.type == 'call_permission_request'

    updated_attributes = { text: @status }
    message = yield update_message_call_permission_request(message, updated_attributes)
    Success message
  end

  private

  def find_message(id)
    Models::Message.find_by(id: id)
  end

  def update_message_call_permission_request(message, attributes)
    message.update(attributes) ? Success(message) : Failure(:update_message_call_permission_request_failed)
  end
end
