# frozen_string_literal: true

RSpec.describe Repositories::Messages::CallPermissionRequest::CreatePermanent do
  describe '#call' do
    let(:organization) { create :organization }
    let(:app_chat_channel) { create :channel_wa, organization: organization }
    let(:contact) { create :contact, organization_id: organization.id, channel_integration_id: app_chat_channel.id, channel: 'wa' }

    context 'when setting permanent call permission' do
      it 'should return true and set the redis key' do
        result = described_class.new(contact.id, 'accept').call
        expect(result).to be_success
        expect(result.success).to eql true
        expect(REDIS_R.get("call_permission_request_#{contact.id}_permanent")).to eql 'accept'
      end
    end
  end
end
