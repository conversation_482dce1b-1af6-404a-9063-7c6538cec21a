# frozen_string_literal: true

RSpec.describe Repositories::Messages::CallPermissionRequest::GetPermanent do
  describe '#call' do
    let(:organization) { create :organization }
    let(:app_chat_channel) { create :channel_wa, organization: organization }
    let(:contact) { create :contact, organization_id: organization.id, channel_integration_id: app_chat_channel.id, channel: 'wa' }

    before do
      REDIS_W.set("call_permission_request_#{contact.id}_permanent", 'accept')
    end

    it 'should return the value from redis' do
      result = described_class.new(contact.id).call
      expect(result).to be_success
      expect(result.success).to eql 'accept'
    end

    it 'should return nil if key does not exist' do
      REDIS_W.del("call_permission_request_#{contact.id}_permanent")
      result = described_class.new(contact.id).call
      expect(result).to be_success
      expect(result.success).to be_nil
    end
  end
end
