# frozen_string_literal: true

class Repositories::Messages::CallPermissionRequest::GetExpired < Repositories::AbstractRepository
  def initialize(message_id, room_id, contact_id)
    @message_id = message_id
    @room_id = room_id
    @contact_id = contact_id
  end

  def call
    # immediately return if permanent permission exist
    return success true if is_permanent_permission_exist @contact_id
    unless is_message_expired
      # update status to expired
      Repositories::Rooms::UpdateRoomCallPermission.new(@room_id, 'expired', @message_id).call
      Repositories::Messages::UpdateMessageCallPermission.new(@message_id, 'expired').call
    end

    success true
  end

  def is_message_expired
    key = "call_permission_request_#{@message_id}_expiration"
    daily = REDIS_R.get key
    daily
  end

  def is_permanent_permission_exist contact_id
    permanent_call_permission_request = Repositories::Messages::CallPermissionRequest::GetPermanent.new(contact_id).call
    permanent_call_permission_request.value!.present? rescue false
  end
end
