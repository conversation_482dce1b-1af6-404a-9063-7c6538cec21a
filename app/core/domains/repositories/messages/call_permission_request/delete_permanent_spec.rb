# frozen_string_literal: true

RSpec.describe Repositories::Messages::CallPermissionRequest::DeletePermanent do
  describe '#call' do
    let(:organization) { create :organization }
    let(:app_chat_channel) { create :channel_wa, organization: organization }
    let(:contact) { create :contact, organization_id: organization.id, channel_integration_id: app_chat_channel.id, channel: 'wa' }

    before do
      REDIS_W.set("call_permission_request_#{contact.id}_permanent", 'accept')
    end

    it 'should delete the redis key and return true' do
      result = described_class.new(contact.id).call
      expect(result).to be_success
      expect(REDIS_R.get("call_permission_request_#{contact.id}_permanent")).to be_nil
    end
  end
end
