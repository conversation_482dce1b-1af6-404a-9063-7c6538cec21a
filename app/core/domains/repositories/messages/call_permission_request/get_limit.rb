# frozen_string_literal: true

class Repositories::Messages::CallPermissionRequest::GetLimit < Repositories::AbstractRepository
  def initialize(contact_id)
    @contact_id = contact_id
  end

  def call
    success({ is_limit: true, daily_limit: 0, weekly_limit: 0 }) if is_permanent_permission_exist(@contact_id)
    daily_limit = get_daily_limit
    weekly_limit = get_weekly_limit

    is_limit = (get_daily_limit.present? && get_daily_limit.to_i >= 1) || (get_weekly_limit.present? && get_weekly_limit.to_i >= 2)

    payload = {
      is_limit:     is_limit,
      daily_limit:  daily_limit.to_i,
      weekly_limit: weekly_limit.to_i
    }
    success payload
  end

  def get_daily_limit
    key = "call_permission_request_daily_limit_#{@contact_id}"
    daily = REDIS_R.get key
    daily
  end

  def get_weekly_limit
    key = "call_permission_request_weekly_limit_#{@contact_id}"
    weekly = REDIS_R.get key
    weekly
  end

  def is_permanent_permission_exist contact_id
    permanent_call_permission_request = Repositories::Messages::CallPermissionRequest::GetPermanent.new(contact_id).call
    permanent_call_permission_request.value!.present? rescue false
  end
end
