# frozen_string_literal: true

RSpec.describe Repositories::Rooms::CallPermissionRequest::GetLast do
  describe '#call' do
    let(:organization) { create(:organization) }
    let(:organization_id) { organization.id }
    let(:recipient_id) { contact.account_uniq_id }
    let(:channel_integration) { create(:channel_wa, organization_id: organization_id) }
    let(:contact) { create(:contact, organization_id: organization_id, channel_integration_id: channel_integration.id) }

    context 'when call permission request message exists' do
      let!(:message) { { message_id: fake_uuid, status: 'waiting' } }
      let!(:room) do
        create(
          :room,
          channel_integration:     channel_integration,
          organization_id:         organization_id,
          account_uniq_id:         recipient_id,
          call_permission_request: [message],
          created_at:              4.hour.ago
        )
      end

      it 'returns Success with message id' do
        repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
        result = repository.call
        expect(result).to be_success
        expect(result.value!).to eq({ id: message[:message_id], text: message[:status], contact_id: contact.id, is_permanent: nil })
      end

      context 'when multiple messages exist' do
        # Override the original message to be older than newer_message
        let!(:older_message) { { message_id: fake_uuid, status: 'accept' } }
        let!(:newer_message) { { message_id: fake_uuid, status: 'reject' } }
        let!(:room) do
          create(
            :room,
            channel_integration:     channel_integration,
            organization_id:         organization_id,
            account_uniq_id:         recipient_id,
            call_permission_request: [older_message, newer_message],
            created_at:              3.hour.ago
          )
        end

        it 'returns the most recent message' do
          repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
          result = repository.call

          expect(result).to be_success
          expect(result.value!).to eq({ id: newer_message[:message_id], text: newer_message[:status], contact_id: contact.id, is_permanent: nil })
        end
      end

      context 'when multiple rooms exist' do
        let!(:older_message) { { message_id: 'abc', status: 'accept' } }
        let!(:newer_message) { { message_id: 'def', status: 'reject' } }
        let!(:newer_room) do
          create(
            :room,
            channel_integration:     channel_integration,
            organization_id:         organization_id,
            account_uniq_id:         recipient_id,
            call_permission_request: [newer_message],
            created_at:              1.hours.ago,
          )
        end
        let!(:older_room) do
          create(
            :room,
            channel_integration:     channel_integration,
            organization_id:         organization_id,
            account_uniq_id:         recipient_id,
            call_permission_request: [older_message],
            created_at:              2.hours.ago,
            status:                  'resolved'
          )
        end

        before do
          newer_room
          older_room
        end

        it 'returns the most recent message' do
          repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
          result = repository.call

          expect(result).to be_success
          expect(result.value!).to eq({ id: newer_message[:message_id], text: newer_message[:status], contact_id: contact.id, is_permanent: nil })
        end
      end
    end

    context 'when call permission request message does not exist' do
      it 'returns Failure with message_call_permission_request_not_found' do
        repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
        result = repository.call

        expect(result).to be_failure
        expect(result.failure).to eq(:message_call_permission_request_not_found)
      end
    end

    context 'when message exists but for different recipient' do
      let!(:message) { { message_id: fake_uuid, status: 'accept' } }
      let!(:room) do
        create(
          :room,
          channel_integration:     channel_integration,
          organization_id:         organization_id,
          account_uniq_id:         'other_recipient_id',
          call_permission_request: [message]
        )
      end

      it 'returns Failure with message_call_permission_request_not_found' do
        repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
        result = repository.call

        expect(result).to be_failure
        expect(result.failure).to eq(:message_call_permission_request_not_found)
      end
    end

    context 'when message exists but for different organization' do
      let(:different_organization) { create(:organization) }
      let!(:message) { { message_id: fake_uuid, status: 'accept' } }
      let!(:room) do
        create(
          :room,
          channel_integration:     channel_integration,
          organization_id:         different_organization.id,
          account_uniq_id:         recipient_id,
          call_permission_request: [message]
        )
      end

      it 'returns Failure with message_call_permission_request_not_found' do
        repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
        result = repository.call

        expect(result).to be_failure
        expect(result.failure).to eq(:message_call_permission_request_not_found)
      end
    end

    context 'edge cases' do
      context 'when call_permission_request is nil' do
        let!(:room) do
          create(
            :room,
            channel_integration:     channel_integration,
            organization_id:         organization_id,
            account_uniq_id:         recipient_id,
            call_permission_request: nil
          )
        end

        it 'returns Failure with message_call_permission_request_not_found' do
          repository = described_class.new(organization_id: organization_id, recipient_id: recipient_id)
          result = repository.call

          expect(result).to be_failure
          expect(result.failure).to eq(:message_call_permission_request_not_found)
        end
      end

      context 'when organization_id is nil' do
        it 'returns Failure with organization_id_not_found' do
          repository = described_class.new(organization_id: nil, recipient_id: recipient_id)
          result = repository.call

          expect(result).to be_failure
          expect(result.failure).to eq(:organization_id_not_found)
        end
      end

      context 'when recipient_id is nil' do
        it 'returns Failure with recipient_id_not_found' do
          repository = described_class.new(organization_id: organization_id, recipient_id: nil)
          result = repository.call

          expect(result).to be_failure
          expect(result.failure).to eq(:recipient_id_not_found)
        end
      end
    end
  end
end
