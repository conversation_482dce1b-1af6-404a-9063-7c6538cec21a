# frozen_string_literal: true

class Repositories::Rooms::CallPermissionRequest::GetLast < Repositories::AbstractRepository
  def initialize(organization_id:, recipient_id:)
    @organization_id = organization_id
    @recipient_id = recipient_id
  end

  def call
    return failure(:organization_id_not_found) if @organization_id.blank?
    return failure(:recipient_id_not_found) if @recipient_id.blank?

    room = Models::Room.select(:id, :call_permission_request, :channel_integration_id)
      .where(organization_id: @organization_id, account_uniq_id: @recipient_id)
      .where.not(call_permission_request: nil)
      .order(created_at: :desc)
      .limit(1)
      .first

    if room.present? && room.call_permission_request.is_a?(Array)
      contact = Models::Contact.find_by(phone_number: @recipient_id, channel_integration_id: room.channel_integration_id)
      is_permanent_permission_exist = is_permanent_permission_exist contact.id
      call_permission_request = room.call_permission_request.last
      return success({ id: call_permission_request['message_id'], text: call_permission_request['status'], contact_id: contact.id, is_permanent: is_permanent_permission_exist.success }) if call_permission_request.present?
    end

    failure(:message_call_permission_request_not_found)
  end

  def is_permanent_permission_exist contact_id
    permanent_call_permission_request = Repositories::Messages::CallPermissionRequest::GetPermanent.new(contact_id).call
    permanent_call_permission_request
  end
end
````````~~~~`````````````