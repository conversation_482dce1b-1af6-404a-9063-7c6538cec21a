# frozen_string_literal: true

class Builders::Contact < Builders::AbstractBuilder
  acts_as_builder_for_entity Entities::Contact

  def attributes_for_entity
    {
      full_name:          ar_model_instance.full_name,
      avatar:             ar_model_instance.avatar.as_json,
      email:              ar_model_instance.extra['email'] || ar_model_instance.extra['email_address'] || '',
      username:           ar_model_instance.extra['username'] || '',
      channel:            ar_model_instance.channel || '',
      code:               ar_model_instance.code || '',
      is_blocked:         ar_model_instance.is_blocked || false,
      extra:              ar_model_instance.try(:contact_extra) || ar_model_instance.try(:extra) || {},
      qontak_customer_id: ar_model_instance.qontak_customer_id || ''
    }
  end
end
