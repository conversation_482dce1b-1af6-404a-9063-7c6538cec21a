# frozen_string_literal: true

class Builders::Masks::ContactObject < Builders::AbstractBuilder
  acts_as_builder_for_entity Entities::ContactObject

  def attributes_for_entity
    {
      full_name:          ar_model_instance.full_name,
      avatar:             ar_model_instance.avatar.as_json,
      email:              ar_model_instance.extra['email'].to_sensor || ar_model_instance.extra['email_address'].to_sensor || '',
      username:           ar_model_instance.extra['username'].to_sensor || '',
      channel:            ar_model_instance.channel || '',
      code:               ar_model_instance.code || '',
      is_blocked:         ar_model_instance.is_blocked || false,
      extra:              mask_extra(ar_model_instance&.extra) || {},
      account_uniq_id:    ar_model_instance.account_uniq_id.to_sensor,
      phone_number:       ar_model_instance.phone_number.to_sensor,
      last_activity_at:   ar_model_instance.updated_at,
      qontak_customer_id: ar_model_instance&.qontak_customer_id || ''
    }.merge(build_attributes)
  end

  def build_attributes
    response = []
    if ar_model_instance.try(:childs).present?
      response = ar_model_instance.childs
    elsif defined? ar_model_instance.handlers
      response = ar_model_instance.handlers.select(:id, :account_uniq_id, :channel, :channel_integration_id, :authority, :extra).as_json
    end
    { childs: response.map { |res| res.update('account_uniq_id' => res['account_uniq_id'].to_sensor, 'extra' => mask_extra(res['extra'])) } }
  end
end
