# frozen_string_literal: true

class Builders::Masks::Contact < Builders::AbstractBuilder
  acts_as_builder_for_entity Entities::Contact

  def attributes_for_entity
    contact_extra = ar_model_instance.try(:contact_extra) || ar_model_instance.try(:extra) || {}
    {
      full_name:          ar_model_instance.full_name,
      avatar:             ar_model_instance.avatar.as_json,
      email:              ar_model_instance.extra['email'].to_sensor || ar_model_instance.extra['email_address'].to_sensor || '',
      username:           ar_model_instance.extra['username'].to_sensor || '',
      channel:            ar_model_instance.channel || '',
      code:               ar_model_instance.code || '',
      is_blocked:         ar_model_instance.is_blocked || false,
      extra:              mask_extra(contact_extra),
      account_uniq_id:    ar_model_instance.account_uniq_id.to_sensor,
      phone_number:       ar_model_instance.phone_number.to_sensor,
      qontak_customer_id: ar_model_instance&.qontak_customer_id || ''
    }
  end
end
