# frozen_string_literal: true

class Entities::ContactObject < Entities::AbstractEntity
  attribute :id, Types::Strict::String
  attribute :contact_handler_id, Types::Strict::String.optional
  # personality
  attribute :phone_number, Types::Strict::String.optional
  attribute :full_name, Types::Strict::String.optional
  attribute :email, Types::Strict::String.optional
  attribute :username, Types::Strict::String.optional

  # system info
  attribute :authority, Types::Strict::String.optional
  attribute :code, Types::Strict::String.optional
  attribute :created_at, Types::Params::Time
  attribute :updated_at, Types::Params::Time
  attribute :last_activity_at, Types::Params::Time

  attribute :channel, Types::Strict::String.optional
  attribute :status, Types::String.enum('success', 'failed')
  attribute :error_messages, Types::Strict::Hash
  attribute :extra, Types::Strict::Hash
  attribute :account_uniq_id, Types::Strict::String.optional
  attribute :channel_integration_id, Types::Strict::String.optional
  attribute :avatar, Types::Strict::Hash.optional
  attribute :is_valid, Types::Strict::Bool
  attribute :is_blocked, Types::Strict::Bool
  attribute :childs, Types::Strict::Array.optional
  attribute :qontak_customer_id, Types::Strict::String.optional
end
