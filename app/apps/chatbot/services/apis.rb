# frozen_string_literal: true

class Chatbot::Services::Apis < Repositories::AbstractHttp
  def initialize
    @base_url = ENV['CHATBOT_SERVICE_URL']
    @auth_key = ENV['CHATBOT_SERVICE_AUTH_KEY']
    @pigeon = Pigeon::Client.new('chatbot_webhook')
    @data = data
    @extra = extra
  end

  def update_division(organization_id:, division_id:, company_id:)
    return if @base_url.blank? || @auth_key.blank?
    headers = {
      'Authorization': "Basic #{@auth_key}"
    }

    action = 'delete'

    payload = {
      action:          action,
      division_id:     division_id,
      organization_id: organization_id,
      company_id:      company_id
    }

    endpoint = "#{@base_url}/api/v1/chat/divisions"

    response = @pigeon.patch(endpoint, body: payload.as_json, headers: headers)
    parse_response(response)
  end

  def update_channel_division(organization_id:, division_id:, company_id:, channel_id:)
    return if @base_url.blank? || @auth_key.blank?
    headers = {
      'Authorization': "Basic #{@auth_key}"
    }

    action = 'delete'

    payload = {
      action:          action,
      channel_id:      channel_id,
      division_id:     division_id,
      organization_id: organization_id,
      company_id:      company_id
    }

    endpoint = "#{@base_url}/api/v1/chat/divisions/channel_integrations"

    response = @pigeon.patch(endpoint, body: payload.as_json, headers: headers)
    parse_response(response)
  end
end
