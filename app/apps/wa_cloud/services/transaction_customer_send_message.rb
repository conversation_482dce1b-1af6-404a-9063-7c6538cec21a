# frozen_string_literal: true

require 'fileio'
require 'stringio'

# Service to process message from WA Cloud
class WaCloud::Services::TransactionCustomerSendMessage
  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:call, :prepare_message, :fetch_media, :process_media)
  include Services::Elasticsearch::Queries

  def initialize(attrs)
    @attrs = ActiveSupport::HashWithIndifferentAccess.new(attrs)
    @is_wa_group = @attrs[:group_id].present?
    @package_inbox_disabled = false
  end

  def call
    channel_integration = yield Repositories::ChannelIntegrations::FindByWebhook.new(webhook: @attrs[:webhook], target_channel: :wa_cloud, check_active: true).call
    prepare_room_response = prepare_room @attrs, channel_integration
    @package_inbox_disabled = Services::Organizations::UseCasePackage.new(prepare_room_response[:room].organization).is_flow_inbox_disabled?
    prepare_message_response = yield prepare_message @attrs, prepare_room_response

    if @attrs[:referral].present?
      update_room_description_referral! @attrs, prepare_room_response
      create_referral_message prepare_room_response[:room], prepare_message_response
    end

    system_create_resend_chat_message(prepare_message_response, prepare_room_response[:room]) if @attrs[:is_resend_chat]
    # PLEASE DO NOT MOVE THIS LINE
    return Success Hashie::Mash.new({ message: prepare_message_response }) if @package_inbox_disabled

    if @attrs[:form_response].present? && @attrs[:context].present? && @attrs[:context][:id] && prepare_room_response[:room]
      data = { form_response: @attrs[:form_response], form_message_external_id: @attrs[:context][:id], room_created_at: prepare_room_response[:room].created_at.to_i, submitted_at: @attrs[:time], external_id: @attrs[:external_id] }
      if Rails.env.test?
        WaFlows::SubmitFlowResponseWorker.new.perform(data)
      else
        WaFlows::SubmitFlowResponseWorker.perform_async(data)
      end
    end
    send_notification(prepare_message_response, prepare_room_response)

    unless prepare_room_response[:is_first_message] && Services::Preference.new.enabled?(:campaign_without_room)
      auto_assign_agent(prepare_room_response[:room])
    end
    Publishers::WaInbounds::CreateAnalytic.new(channel_integration.id, @attrs[:sender_id], prepare_room_response).publish
    if prepare_room_response[:is_first_message]
      if Services::Preference.new.enabled?(:campaign_without_room) && !Services::Preference.new.enabled?(:new_campaign_without_room)
        # Prepare parameters for CreateBubbleLastCampaignWorker
        buttons = prepare_message_response.buttons
        has_button = buttons&.first&.dig('type') == 'BUTTON'

        campaign_params = {
          room_id:            prepare_room_response.dig(:room)&.id,
          room_created_at:    prepare_room_response.dig(:room)&.created_at,
          contact:            prepare_room_response.dig(:contact)&.as_json || {},
          bot_participant:    prepare_room_response.dig(:bot_participant)&.as_json || {},
          message_id:         prepare_message_response&.id,
          external_reply_id:  @attrs.dig(:context, :id),
          message_has_button: has_button,
          is_wa_group:        @is_wa_group
        }

        campaign_params[:button_value] = buttons.first['text'] if has_button
        CreateBubbleLastCampaignWorker.perform_async(campaign_params)
      end
      Services::Webhooks::RoomInteraction.new(room: prepare_room_response[:room], event: :room_created).deliver
      Services::Webhooks::Mekari::Room.new(room: prepare_room_response[:room], event: :room_created, first_message: prepare_message_response).deliver if Services::Preference.new.enabled?(:enable_webhook_room_to_mekari)
    end
    process_meta_conversion_event(prepare_room_response[:room], prepare_message_response)
    delete_idle_customer(prepare_message_response.room_id)
    Success Hashie::Mash.new({ message: prepare_message_response })
  end

  private

  def delete_idle_customer(room_id)
    Services::IdleCustomers::Delete.new(room: room_id).call
  end

  ## main methods
  def prepare_room attrs, channel_integration
    account_uniq_id = @is_wa_group ? attrs[:group_id] : attrs[:account_uniq_id]
    room = fetch_room channel_integration.id, account_uniq_id
    if room.present?
      is_first_message = false
      if room.status == 'campaign'
        Repositories::Rooms::UpdateStatus.new(id: room.id, from: 'campaign', to: 'unassigned', room: room).call
        is_first_message = true
      end

      if @is_wa_group
        contact = Models::Contact.find_by(organization_id: room.organization_id, account_uniq_id: attrs[:account_uniq_id], channel_integration_id: room.channel_integration_id)
        participant = room.customer_participants.find_by(contact_able_id: contact.id, created_at: room.created_at..Time.zone.now)
      else
        Services::Participants::Customer.new(room_id: room.id, room_created_at: room.created_at, account_uniq_id: room.account_uniq_id, channel_integration_id: room.channel_integration_id).call unless room.customer_participant.present?
        participant = room.reload.customer_participant
        contact = participant&.contact_able
      end

      if @attrs[:type] == 'text' && attrs.dig(:raw_message, :messages, 0, :interactive, :type) == 'call_permission_reply'
        call_permission_reply = get_call_permission_reply @attrs, room

        Repositories::Rooms::UpdateRoomCallPermission.new(room.id, call_permission_reply[:status], call_permission_reply[:message_id]).call
        updated_message = Repositories::Messages::UpdateMessageCallPermission.new(call_permission_reply[:message_id], call_permission_reply[:status]).call if call_permission_reply[:message_id].present?
        # create limit when user change permission from user preference
        Repositories::Messages::CallPermissionRequest::CreateLimit.new(contact_id: contact.id, room_id: room.id, organization_id: room.organization_id, phone_number: contact.phone_number).call unless call_permission_reply[:message_id].present?
        # create permanent call permission request if user give permanent permission
        if call_permission_reply[:is_permanent]
          Repositories::Messages::CallPermissionRequest::CreatePermanent.new(contact.id, call_permission_reply[:status]).call
        else
          # delete permanent if receive non permanent permission
          Repositories::Messages::CallPermissionRequest::DeletePermanent.new(contact.id).call
        end
        if updated_message.present? && updated_message.success?
          # send notification
          update_notification(updated_message.success, participant)
        else
          last_call_permission_message = Models::Message.find_by(external_id: call_permission_reply[:last_permission_id])
          update_notification(last_call_permission_message, participant) if last_call_permission_message.present?
        end
      end

      CentralizedContacts::Services::SyncContactToCustomer.new(
        contact:         contact,
        target_channel:  contact.channel || 'wa_cloud',
        account_uniq_id: contact.account_uniq_id
      ).call

      { room: room, participant: participant, is_first_message: is_first_message }
    else
      new_room = create_room attrs, channel_integration
      return new_room.success if new_room.success?
      Rails.logger.error "[WA_CLOUD][INBOUND] Failed to create room #{new_room.failure.as_json} | params: #{attrs.as_json}"
      Rollbar.error(new_room.failure.as_json, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
      { room: false, participant: false, is_first_message: false }
    end
  end

  def get_call_permission_reply attrs, room
    messages        = attrs[:raw_message][:messages][0]
    call_permission = messages.dig(:interactive, :call_permission_reply)

    status = call_permission[:response]

    is_permanent =
      if status == 'reject'
        true
      else
        call_permission[:is_permanent] || false
      end
    context_id = messages.dig(:context, :id)

    message = Models::Message.find_by(external_id: context_id)
    last_permission = Repositories::Rooms::CallPermissionRequest::GetLast
      .new(organization_id: room.organization_id, recipient_id: room.account_uniq_id)
      .call

    {
      message_id:         message&.id,
      last_permission_id: last_permission.success[:id],
      status:             status,
      is_permanent:       is_permanent
    }
  end

  def resolve_room(room)
    resolved_room = Services::Room::ResolveRetention.new(room.id).call
    Services::Datadog::CaptureCustomMetric.new(name: :single_auto_resolve_retention_room, tags: ['channel:wa_cloud']).capture
    return nil if resolved_room.success?

    room
  end

  def prepare_message attrs, prepare_room_response
    room             = prepare_room_response[:room]
    is_first_message = prepare_room_response[:is_first_message]
    participant      = prepare_room_response[:participant]
    context_id = attrs.dig(:context, :id)
    referred_product = attrs.dig(:raw_message, :messages, 0, :context, :referred_product)

    if attrs[:file].present?
      is_allowed = is_allowed_media(attrs, room)
      return Failure("Received disallowed media type: #{attrs['file_type']}") unless is_allowed

      if Services::Preference.new.enabled?(:message_file_worker_wa_cloud)
        media = yield process_media(attrs, room.organization_id, room.id)
        attrs.merge!(media)
      else
        attrs[:file] = yield fetch_media attrs, organization: room.organization
      end
    end
    if referred_product.present?
      attrs[:raw_message][:product] = get_product(room.organization_id, referred_product.dig(:catalog_id), referred_product.dig(:product_retailer_id))
      attrs[:raw_message][:message_type] = 'item'
      attrs[:type] = 'product'
    end
    attrs[:reply_id]        = fetch_reply_id(context_id, room) if context_id.present?
    attrs[:participant_id]  = participant.id
    attrs[:organization_id] = room.organization_id
    attrs[:room_id]         = room.id
    attrs[:sender_id]       = participant.contact_able_id
    attrs[:sender_type]     = participant.contact_able_type
    attrs[:created_at]      = Time.at(attrs[:time]).utc.to_datetime
    attrs[:text]            = sanitize_unsupported_unicode(attrs[:text]) if attrs[:text].present?
    attrs[:raw_message]     = sanitize_unsupported_unicode_in_hash(attrs[:raw_message]) if attrs[:raw_message].present?
    create_message(attrs.except(:phone_number, :name, :webhook, :account_uniq_id, :file_name, :file_type, :context, :time, :remote_file_url, :is_resend_chat, :referral, :group_id, :form_response), is_first_message, context_id, prepare_room_response)
  end

  def get_product(organization_id, catalog_id, retailer_id)
    organization = Services::Redis::Organizations::Get.new(organization_id).call
    service         = WaCloud::Services::ApisAdapter.new(organization.settings).call
    response        = service.get_product(catalog_id, retailer_id)

    if response.failure?
      Failure(response.failure[:error][:message])
    else
      product = response.success[:data].first

      # upload image to oss
      url = product.dig('image_url')
      unless url.nil?
        api = WaCloud::Services::ApisAdapter.new(organization.settings).call
        file_response = api.fetch_media url

        unless file_response.success?
          return Failure 'failed to get product catalog image'
        end

        file_io = StringIO.new(file_response.success)

        def file_io.original_filename
          "product_catalog_#{SecureRandom.uuid}.jpg"
        end

        def file_io.content_type
          'image/jpeg'
        end

        params = {
          file:            {
            filename: file_io.original_filename,
            type:     file_io.content_type,
            tempfile: file_io
          },
          organization_id: organization.id
        }

        upload = Repositories::DirectUploader.new(params: params).call

        unless upload.success?
          return Failure 'failed to upload product catalog image'
        end
        product.image_url = upload.success.url
      end
      WaCloud::Builders::Product.new(Models::Model.new(product.as_json)).build
    end
  end

  def sanitize_unsupported_unicode(string)
    string.delete("\u0000")
  end

  def sanitize_unsupported_unicode_in_hash(hash)
    JSON.parse(hash.to_json.gsub('\\u0000', ''))
  rescue JSON::ParserError
    hash
  end

  def send_notification(message, prepare_room_response)
    participant = prepare_room_response[:participant]
    Services::Notifications::Handlers::WhenCustomer::SendMessage.new(message, sender: participant).call
  end

  def update_notification(message, participant)
    Services::Notifications::Handlers::WhenSystem::UpdateMessage.new(message, sender: participant).call
  end

  ## helpers create
  def create_room attrs, channel_integration
    params = attrs.merge(
               channel_integration_id: channel_integration.id,
               organization_id:        channel_integration.organization_id,
               target_channel:         channel_integration.target_channel
    )

    Repositories::Rooms::CreateBySql.new(params: params).call
  end

  def is_skip_caa
    res = false
    res ||= @is_wa_group
    res ||= @attrs[:form_response].present? # Inbound is form response

    res
  end

  def is_skip_message_interaction
    res = false
    res ||= @is_wa_group

    res
  end

  def create_message attrs, is_first_message, context_id, room_response
    Repositories::Messages::Creates::Customer.call attrs, is_first_message: is_first_message, is_skip_caa: is_skip_caa, is_skip_message_interaction: is_skip_message_interaction, context_id: context_id, room_response: room_response, package_inbox_disabled: @package_inbox_disabled
  end

  ## helpers fetch
  def fetch_room channel_integration_id, account_uniq_id
    room = Models::Room.not_resolved.find_by(channel_integration_id: channel_integration_id, account_uniq_id: account_uniq_id)
    if room.present?
      if room.is_old? && Services::Preference.new.enabled?(:single_auto_resolve_retention_room_wa_cloud)
        resolved_room = resolve_room(room)
        return nil unless resolved_room.present?

        Rollbar.error("failed to auto resolve retention for room #{room.id}", class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
      end
    end
    room
  end

  def fetch_channel_integration attrs
    resource = Services::Redis::ChannelIntegrations::GetByWebhook.new(attrs[:webhook]).call
    resource ? Success(resource) : Failure(:channel_not_found)
  end

  def update_room_description_referral! attrs, prepare_room_response
    room = prepare_room_response[:room]
    room.description = room.description += ";source_id=#{attrs.dig(:referral, :source_id)};ctwa_clid=#{attrs.dig(:referral, :ctwa_clid)};"
    room.save
  end

  def fetch_reply_id(message_id, room)
    if Services::Preference.new.enabled?(:messages_partition)
      reply_id = Models::Message.find_by(external_id: message_id, created_at: room.created_at..Time.zone.now)&.id
      # this is have hack to handle where external id belongs to a room which is already resolved.
      # this situation happen if customer quote a message from already resolved room.
      # To narrow down search we are gradually search more partitioned table if we don't found results.
      unless reply_id.present?
        max_depth = Services::Preference.new.info(:max_reply_id_serach)&.dig(:value, 'max_days_range').to_i
        delta = Services::Preference.new.info(:max_reply_id_serach)&.dig(:value, 'delta').to_i
        start_date = room.created_at - delta.days
        end_date = room.created_at
        min_start_date = Time.zone.now - max_depth.days
        while reply_id.nil? && (min_start_date < start_date)
          reply_id = Models::Message.find_by(external_id: message_id, created_at: start_date..end_date)&.id
          end_date = start_date
          start_date -= delta.days
        end
      end
      reply_id

    else
      Models::Message.find_by(external_id: message_id)&.id
    end
  end

  def process_media attrs, organization_id, room_id
    message_file_id = SecureRandom.uuid
    if attrs[:file_name].present?
      file_name = attrs[:file_name]
    else
      file_name = yield get_file_name_by_mime(attrs[:file], attrs[:file_type])
    end

    file_name = sanitize_file_name(file_name)
    payload = {
      message_file_id: message_file_id,
      file_id:         attrs[:file],
      file_type:       attrs.try(:file_type),
      file_name:       file_name,
      organization_id: organization_id,
      room_id:         room_id,
      channel:         :wa_cloud,
      message_type:    attrs[:type]
    }
    MessageFileWorker.new.perform(payload.as_json) if Rails.env.test?
    MessageFileWorker.perform_async(payload.as_json) unless Rails.env.test?

    Success({ file_uniq_id: message_file_id, file_url: file_name })
  end

  def fetch_media attrs, organization: nil
    api      = WaCloud::Services::ApisAdapter.new(organization.settings).call
    response = yield api.fetch_media_url attrs[:file]

    url = response.url
    file_response = yield api.fetch_media url
    if attrs[:file_name].present?
      file_name = attrs[:file_name]
    else
      file_name = yield get_file_name_by_mime(attrs[:file], response.mime_type || attrs[:file_type])
    end
    file_name = sanitize_file_name(file_name)
    Success(FileIO.new(file_response, file_name))
  end

  ## for resend chat
  def system_create_resend_chat_message message, room
    params = {
      action:             'resend_chat_after_top_up',
      room_id:            room.id,
      actor_id:           Models::SystemAccount::BOT_ID,
      message_created_at: message.created_at + 2.second,
      room_created_at:    room.created_at
    }
    Repositories::Messages::Creates::System.call(params: params, package_inbox_disabled: @package_inbox_disabled)
  end

  def get_file_name_by_mime file_name, mime_type
    if mime_type.include?(';')
      mime_type = mime_type.split(';').first
    end

    file_extension = ".#{mime_type.split('/').last}"

    if file_extension.present?
      Success(file_name + file_extension)
    else
      Failure 'File type not found'
    end
  end

  def sanitize_file_name(name)
    pattern = /(\'|\"|\*|\\|\)|\$|\+|\(|\^|\!|\~|\`|\,|\%|\&)/

    name = name.scrub
    name = name.tr('\\', '/') # work-around for IE
    name = File.basename(name)
    name = name.gsub(CarrierWave::SanitizedFile.sanitize_regexp, '_')
    name = name.gsub(pattern, '_')
    name = "_#{name}" if /\A\.+\z/.match?(name)
    name = 'unnamed' if name.size.zero?
    name.mb_chars.to_s
  end

  def auto_assign_agent(room)
    return if @is_wa_group # break if wa group
    # Check online agent to minimize queue assign agent
    online_agent = Services::Redis::Organizations::OnlineAgent.new(organization_id: room.organization_id).call
    online_agent = online_agent.nil? ? 0 : online_agent
    return Failure 'There is no online agent' if online_agent <= 0
    # Async auto agent allocation
    Publishers::WaInbounds::AutoAssignAgent.new({ room_id: room.id, organization_id: room.organization_id }).publish
  end

  def process_meta_conversion_event(room, message)
    _, source_id, ctwa_clid = room.description.split(';')
    return unless source_id.present? && ctwa_clid.present?

    source_id = source_id.gsub('source_id=', '')
    ctwa_clid = ctwa_clid.gsub('ctwa_clid=', '')
    params = {
      room_id:                room.id,
      channel_integration_id: room.channel_integration_id,
      message_id:             message.id,
      message_text:           message.text,
      meta_source_id:         source_id,
      meta_uniq_id:           ctwa_clid,
      organization_id:        room.organization_id
    }
    if Rails.env.test?
      MetaConversionEvent::ProcessEventWorker.new.perform(params)
    else
      MetaConversionEvent::ProcessEventWorker.perform_async(params)
    end
  end

  def create_referral_message(room, message)
    params = {
      room_created_at: (room.try(:delta_created_at) || room.created_at).iso8601
    }
    MetaReferral::CreateMessageWorker.perform_async(message.id, params)
  end

  def is_allowed_media attrs, room
    disallowed_media = ['image/svg+xml', 'application/postscript', 'application/xml', 'text/xml', 'text/html']
    if disallowed_media.include?(attrs['file_type'])
      params = {
        action:             'received_disallowed_media',
        room_id:            room.id,
        actor_id:           Models::SystemAccount::BOT_ID,
        message_created_at: attrs['time'] || Time.zone.now,
        room_created_at:    room.created_at
      }
      Repositories::Messages::Creates::System.call(params: params)
      return false
    end
    true
  end
end
