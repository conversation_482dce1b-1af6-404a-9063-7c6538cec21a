# frozen_string_literal: true

class WaCloud::Services::SystemSendCallsSdpAnswer
  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:call)
  include Repositories::Billings::Helpers

  def initialize(attr)
    @attrs = Hashie::Mash.new(attr)
  end

  def call
    channel_integration = yield Repositories::ChannelIntegrations::FindByWebhook.new(webhook: @attrs[:webhook], target_channel: :wa_cloud, check_active: true).call
    message = Models::Message.find_by(external_id: @attrs.dig(:calls, 0, :id))
    if message.nil?
      cached_message = fetch_message_from_cache(@attrs.dig(:calls, 0, :id))

      if cached_message.nil?
        Rollbar.error('Message wa call is nil!', class: self.class, attrs: @attrs.as_json) if Rails.env.staging?
        return Failure 'Message wa call is missing'
      end
      message = yield cached_message if cached_message.present?
    end
    prepare_room_response = prepare_room @attrs[:calls], channel_integration, message

    # prepare_message_kafka_response will be used on kafka message publish
    message_to_kafka = prepare_message_kafka prepare_room_response, @attrs[:calls], message
    KafkaProducers::WaCall::RecordActivity.new(message_to_kafka).publish

    payload = prepare_payload(@attrs[:calls])

    # if call rejected, dont push another webhook (complete and terminate) to fcm or update bubble
    if message && message.text != 'Declined'
      calls_attr = @attrs[:calls][0]
      recipient_id = calls_attr[:to] || calls_attr[:recipient_id]
      message_call_permission_request = fetch_message_call_permission_request(message.organization_id, recipient_id)
      message = update_message(message, message_call_permission_request)
      send_sdp_notification(payload, prepare_room_response)
      send_message_notification(message, prepare_room_response)
    end

    Success Hashie::Mash.new({ message: payload })
  end

  def set_redis_wa_call_sdp(external_id, sdp)
    Services::Redis::Calls::WaCallSdp.new(external_id: external_id, sdp: sdp).call
  end

  def prepare_payload data
    payload = data
    attr = payload[0].merge(event_type: 'calls')
    payload[0] = attr
    payload
  end

  def build_message message
    Builders::Message.new(message).build
  end

  def prepare_room attrs, channel_integration, message
    account_uniq_id = attrs[0][:to] || attrs[0][:recipient_id]
    room = fetch_room channel_integration.id, account_uniq_id
    if room.present?
      Services::Participants::Customer.new(room_id: room.id, room_created_at: room.created_at, account_uniq_id: room.account_uniq_id, channel_integration_id: room.channel_integration_id).call unless room.customer_participant.present?
      participant = room.reload.customer_participant
      { room: room, participant: participant }
    elsif account_uniq_id.nil?
      room = Models::Room.not_resolved.find(message.room_id)
      if room.present?
        Services::Participants::Customer.new(room_id: room.id, room_created_at: room.created_at, account_uniq_id: room.account_uniq_id, channel_integration_id: room.channel_integration_id).call unless room.customer_participant.present?
        participant = room.reload.customer_participant
        { room: room, participant: participant }
      end
    else
      { room: nil, participant: nil }
    end
  end

  def update_message message, message_call_permission_request
    if is_ringing
      Repositories::Rooms::UpdateRoomCallPermission.new(message.room_id, 'on call', nil).call
      Repositories::Messages::UpdateMessageCallPermission.new(message_call_permission_request[:id], 'on call').call if message_call_permission_request.present? && !message_call_permission_request[:is_permanent].present?
    elsif is_unanswered
      message.text = 'Unanswered'
      Repositories::Rooms::UpdateRoomCallPermission.new(message.room_id, 'accept', nil).call
      Repositories::Messages::UpdateMessageCallPermission.new(message_call_permission_request[:id], 'accept').call if message_call_permission_request.present? && !message_call_permission_request[:is_permanent].present?
      manage_unanswered_count message
    elsif is_rejected
      message.text = 'Declined'
      Repositories::Rooms::UpdateRoomCallPermission.new(message.room_id, 'accept', nil).call
      Repositories::Messages::UpdateMessageCallPermission.new(message_call_permission_request[:id], 'accept').call if message_call_permission_request.present? && !message_call_permission_request[:is_permanent].present?
    elsif is_answered
      Repositories::Messages::Calls::CreateLimit.new(phone_number: @attrs&.calls&.first&.to, room_id: message.room_id, organization_id: message.room.organization_id).call
      message.text = prepare_duration(@attrs.dig(:calls, 0, :duration)&.to_i || 0)
      # reset last permission to call_complete
      Repositories::Rooms::UpdateRoomCallPermission.new(message.room_id, 'accept', nil).call
      Repositories::Messages::UpdateMessageCallPermission.new(message_call_permission_request[:id], 'accept').call if message_call_permission_request.present? && !message_call_permission_request[:is_permanent].present?
      # reset_limit_request message
    end
    message.save
    build_message(message)
  end

  def manage_unanswered_count message
    resource_contact = Models::Contact.find_by(organization_id: message.room.organization_id, account_uniq_id: message.room.account_uniq_id, channel_integration_id: message.room.channel_integration_id)
    count_unanswered = Services::Redis::Calls::GetWaCallUnansweredCount.new(contact_id: resource_contact.id).call
    # add count unanswered logic if nil set 1 and if count unanswered >= 3 set reset limit request
    if count_unanswered.nil?
      Services::Redis::Calls::SetWaCallUnansweredCount.new(contact_id: resource_contact.id, count: 1).call
    else
      count_unanswered = count_unanswered.to_i
      if count_unanswered >= 3
        reset_limit_request message
      else
        Services::Redis::Calls::SetWaCallUnansweredCount.new(contact_id: resource_contact.id, count: count_unanswered + 1).call
      end
    end
  end

  def reset_limit_request message
    resource_contact = Models::Contact.find_by(organization_id: message.room.organization_id, account_uniq_id: message.room.account_uniq_id, channel_integration_id: message.room.channel_integration_id)
    Repositories::Messages::CallPermissionRequest::ResetLimit.new(resource_contact.id).call
  end

  def fetch_room channel_integration_id, account_uniq_id
    room = Models::Room.not_resolved.find_by(channel_integration_id: channel_integration_id, account_uniq_id: account_uniq_id)
    if room.present?
      if room.is_old? && Services::Preference.new.enabled?(:single_auto_resolve_retention_room_wa_cloud)
        resolved_room = resolve_room(room)
        return nil unless resolved_room.present?

        Rollbar.error("failed to auto resolve retention for room #{room.id}", class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
      end
    end
    room
  end

  def fetch_customer(contact_id)
    customer = Models::Contact.find_by(id: contact_id)
    Rails.logger.warn("Customer with id #{contact_id} not found") unless customer
    customer
  end

  def fetch_agent(user_id)
    agent = Models::User.find_by(id: user_id)
    Rails.logger.warn("User with id #{user_id} not found") unless agent
    agent
  end

  def prepare_message_kafka prepare_room, attrs, message
    event = attrs[0][:event]
    room = prepare_room[:room]
    return { data: { calls: attrs } } unless event == 'connect' && room.present?

    session = attrs[0][:session]

    if session.present?
      set_redis_wa_call_sdp(attrs[0][:id], session[:sdp])
    end

    has_balance = has_balance_voice room.organization_id

    data_room = build_kafka_room_data room, message, prepare_room[:participant]
    { room: data_room, data: { calls: attrs }, has_balance: has_balance }
  end

  def build_kafka_room_data(room, message, customer_participant)
    agent_response = fetch_agent(message.sender_id)
    customer_response = fetch_customer(customer_participant.contact_able_id)
    organization_sso_id = room.organization.sso_id
    organization_unified_sso_id = room.organization.unified_sso_id
    data_room = {}

    data_room[:room_id] = room.id
    data_room[:channel_integration_id] = room.channel_integration_id
    data_room[:agent_id] = agent_response&.id
    data_room[:agent_name] = agent_response&.full_name
    data_room[:agent_sso_id] = agent_response&.sso_id
    data_room[:contact_id] = customer_response&.id
    data_room[:contact_name] = customer_response&.full_name
    data_room[:contact_number] = customer_response&.phone_number
    data_room[:organization_id] = room.organization_id
    data_room[:organization_sso_id] = organization_sso_id
    data_room[:organization_unified_id] = organization_unified_sso_id
    data_room
  end

  def send_sdp_notification(data, prepare_room_response)
    participant = prepare_room_response[:participant]
    room = prepare_room_response[:room]
    Services::Notifications::Handlers::WhenSystem::SendSdp.new(data[0].except(:connection), sender: participant, room: room).call
  end

  def send_message_notification(message, prepare_room_resp)
    participant = prepare_room_resp[:participant]
    Services::Notifications::Handlers::WhenCustomer::SendMessage.new(message, sender: participant).call
  end

  private

  def is_ringing
    @attrs.dig(:calls, 0, :status) == 'RINGING'
  end

  def is_unanswered
    @attrs.dig(:calls, 0, :status) == 'COMPLETED' && @attrs.dig(:calls, 0, :event) == 'terminate' && !@attrs.dig(:calls, 0, :duration).present?
  end

  def is_rejected
    @attrs.dig(:calls, 0, :status) == 'REJECTED'
  end

  def is_answered
    @attrs.dig(:calls, 0, :status) == 'COMPLETED' && @attrs.dig(:calls, 0, :event) == 'terminate' && @attrs.dig(:calls, 0, :duration).present?
  end

  def prepare_duration duration
    hours = duration / 3600
    minutes = (duration % 3600) / 60
    remaining_seconds = duration % 60

    if hours > 0
      "#{hours} hour#{'s' if hours > 1} #{minutes} min #{remaining_seconds} sec"
    elsif minutes > 0
      "#{minutes} min #{remaining_seconds} sec"
    else
      "#{remaining_seconds} sec"
    end
  end

  def fetch_message_from_cache(external_id)
    # rubocop:disable Security/Eval
    data_cache = Services::Redis::Messages::GetWaCall.new(external_id).call
    return nil unless data_cache.present?
    data_cache_hash = eval(data_cache)
    agent_cache = Services::Redis::Users::GetBySsoId.new(data_cache_hash[:sender_sso_id]).call
    return nil unless agent_cache.present?
    param_builder_wa_call = {
      room_id:         data_cache_hash[:room_id],
      organization_id: data_cache_hash[:organization_id],
      sender_id:       agent_cache&.id,
      sender_type:     data_cache_hash[:sender_type],
      type:            data_cache_hash[:type],
      external_id:     external_id,
      text:            'Ongoing'
    }
    WaCloud::Services::BuilderWaCallSendMessage.new(param_builder_wa_call).call
    # rubocop:enable Security/Eval
  end

  def fetch_message_call_permission_request(organization_id, recipient_id)
    message = Repositories::Rooms::CallPermissionRequest::GetLast.new(organization_id: organization_id, recipient_id: recipient_id).call
    Rails.logger.warn('Message call_permission_request not found') unless message.success?
    return nil unless message.success?
    message.success
  end
end
