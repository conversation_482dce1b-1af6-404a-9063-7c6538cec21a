# frozen_string_literal: true

class WaCloud::Interactors::AgentGetCallsPermissionLimit < Interactors::AbstractIteractor
  contract do
    params do
      required(:contact_id).filled(:string)
      required(:room_id).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    attrs = yield result_of_validating_params

    is_limit = yield Repositories::Messages::CallPermissionRequest::GetLimit.new(attrs[:contact_id]).call
    # also change status message to expired if call permission is expired
    room = Models::Room.find(attrs[:room_id])
    if room.present?
      call_permission_request = room.call_permission_request
      if call_permission_request.present?
        call_permission_request.each do |permission|
          Repositories::Messages::CallPermissionRequest::GetExpired.new(permission['message_id'], attrs[:room_id], attrs[:contact_id]).call
        end
      end
    end

    Success is_limit
  end
end
